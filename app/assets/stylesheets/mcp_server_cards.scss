.mcp-server-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }

  &__title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    flex: 1;
    margin-right: 1rem;
  }

  &__price {
    font-size: 1.1rem;
    font-weight: 700;
    color: #059669;
    background: #ecfdf5;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    white-space: nowrap;
  }

  &__description {
    color: #64748b;
    line-height: 1.5;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  &__metadata {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
  }

  &__endpoint,
  &__tools,
  &__github {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #64748b;

    svg {
      width: 16px;
      height: 16px;
      flex-shrink: 0;
    }
  }

  &__endpoint {
    .endpoint-url {
      font-family: monospace;
      background: #f1f5f9;
      padding: 0.125rem 0.5rem;
      border-radius: 4px;
      font-size: 0.8rem;
    }
  }

  &__github a {
    color: #667eea;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  &__stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
  }

  .stat {
    text-align: center;

    &__label {
      display: block;
      font-size: 0.75rem;
      color: #64748b;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-bottom: 0.25rem;
    }

    &__value {
      font-weight: 600;
      color: #1e293b;
      font-size: 0.875rem;
    }
  }

  .health-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;

    svg {
      width: 12px;
      height: 12px;
    }

    &--green {
      background: #dcfce7;
      color: #166534;
    }

    &--red {
      background: #fef2f2;
      color: #dc2626;
    }

    &--gray {
      background: #f1f5f9;
      color: #64748b;
    }
  }

  &__seller {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;

    .seller-label {
      color: #64748b;
    }

    .seller-link {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  &__tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;

    .tag {
      background: #f1f5f9;
      color: #64748b;
      padding: 0.25rem 0.5rem;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 500;

      &--more {
        background: #e2e8f0;
        color: #475569;
      }
    }
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;

    .created-date {
      font-size: 0.875rem;
      color: #64748b;
    }

    .btn {
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-size: 0.875rem;
      font-weight: 500;
      text-decoration: none;
      border: none;
      cursor: pointer;
      transition: all 0.2s;

      &--primary {
        background: #667eea;
        color: white;

        &:hover {
          background: #5a67d8;
        }
      }

      &--secondary {
        background: #f1f5f9;
        color: #64748b;
        border: 1px solid #e2e8f0;

        &:hover {
          background: #e2e8f0;
        }
      }

      &--small {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
      }
    }
  }
}

// Health Monitoring Dashboard Styles
.health-monitoring-dashboard {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;

    h2 {
      font-size: 1.875rem;
      font-weight: 700;
      color: #1e293b;
      margin: 0;
    }
  }

  .health-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .health-metric-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;

    &__icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        width: 24px;
        height: 24px;
      }
    }

    &--blue .health-metric-card__icon {
      background: #dbeafe;
      color: #2563eb;
    }

    &--green .health-metric-card__icon {
      background: #dcfce7;
      color: #16a34a;
    }

    &--red .health-metric-card__icon {
      background: #fef2f2;
      color: #dc2626;
    }

    &--purple .health-metric-card__icon {
      background: #f3e8ff;
      color: #9333ea;
    }

    &--orange .health-metric-card__icon {
      background: #fef3c7;
      color: #d97706;
    }

    &__content {
      flex: 1;
    }

    &__title {
      font-size: 0.875rem;
      color: #64748b;
      margin: 0 0 0.25rem 0;
      font-weight: 500;
    }

    &__value {
      font-size: 1.5rem;
      font-weight: 700;
      color: #1e293b;
      margin: 0 0 0.25rem 0;
    }

    &__subtitle {
      font-size: 0.75rem;
      color: #64748b;
    }
  }

  .uptime-trends-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1e293b;
    }

    .uptime-chart {
      display: flex;
      gap: 1rem;
      align-items: end;
      height: 120px;
      padding: 1rem 0;
    }

    .uptime-bar-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
    }

    .uptime-date {
      font-size: 0.75rem;
      color: #64748b;
      writing-mode: horizontal-tb;
    }

    .uptime-bar-wrapper {
      height: 80px;
      width: 20px;
      background: #f1f5f9;
      border-radius: 4px;
      display: flex;
      align-items: end;
    }

    .uptime-bar-fill {
      width: 100%;
      border-radius: 4px;
      transition: height 0.3s ease;
    }

    .uptime-percentage-label {
      font-size: 0.75rem;
      font-weight: 500;
      color: #374151;
    }
  }

  .servers-health-table-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1e293b;
    }

    .table-container {
      overflow-x: auto;
    }

    .servers-health-table {
      width: 100%;
      border-collapse: collapse;

      th, td {
        padding: 0.75rem;
        text-align: left;
        border-bottom: 1px solid #e2e8f0;
      }

      th {
        font-weight: 600;
        color: #374151;
        background: #f8fafc;
        font-size: 0.875rem;
      }

      .server-info {
        .server-url {
          font-weight: 500;
          color: #1e293b;
          font-family: monospace;
          font-size: 0.875rem;
        }

        .server-meta {
          font-size: 0.75rem;
          color: #64748b;
          margin-top: 0.25rem;
        }
      }

      .uptime-cell {
        .uptime-percentage {
          font-weight: 600;
          color: #1e293b;
        }

        .uptime-bar {
          width: 60px;
          height: 4px;
          background: #e2e8f0;
          border-radius: 2px;
          margin-top: 0.25rem;
          overflow: hidden;
        }

        .uptime-fill {
          height: 100%;
          background: #10b981;
          transition: width 0.3s ease;
        }
      }

      .response-time {
        font-weight: 500;
        color: #1e293b;
      }

      .failure-count {
        color: #dc2626;
        font-weight: 500;
        font-size: 0.875rem;
      }
    }
  }
}

@media (max-width: 768px) {
  .mcp-server-card {
    &__header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    &__stats {
      grid-template-columns: repeat(2, 1fr);
    }

    &__footer {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;

      .btn {
        width: 100%;
        text-align: center;
      }
    }
  }

  .health-monitoring-dashboard {
    padding: 1rem;

    .health-metrics-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .uptime-chart {
      gap: 0.5rem;
    }

    .servers-health-table {
      font-size: 0.875rem;

      th, td {
        padding: 0.5rem;
      }
    }
  }
}
