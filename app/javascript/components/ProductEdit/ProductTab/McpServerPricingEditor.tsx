import * as React from "react";
import { CurrencyCode, formatPriceCentsWithCurrencySymbol } from "$app/utils/currency";
import { PriceInput } from "$app/components/PriceInput";
import { Toggle } from "$app/components/Toggle";
import { Icon } from "$app/components/Icons";

type PricingTier = {
  max_requests: number | null;
  price_cents_per_request: number;
  name: string;
};

type McpServerPricingData = {
  pricing_model: "fixed" | "per_request" | "hybrid";
  per_request_price_cents: number;
  monthly_request_limit: number | null;
  free_tier_requests: number;
  pricing_tiers: PricingTier[];
};

type McpServerPricingEditorProps = {
  pricingData: McpServerPricingData;
  basePriceCents: number;
  currencyType: CurrencyCode;
  onChange: (pricingData: McpServerPricingData) => void;
};

const PricingModelSelector = ({ 
  value, 
  onChange 
}: { 
  value: string; 
  onChange: (value: "fixed" | "per_request" | "hybrid") => void;
}) => (
  <fieldset>
    <legend>Pricing Model</legend>
    <div className="pricing-model-options">
      <label className="pricing-model-option">
        <input
          type="radio"
          name="pricing_model"
          value="fixed"
          checked={value === "fixed"}
          onChange={(e) => onChange(e.target.value as "fixed")}
        />
        <div className="option-content">
          <strong>Fixed Price</strong>
          <p>Customers pay a one-time fee for unlimited access to your MCP server</p>
        </div>
      </label>
      
      <label className="pricing-model-option">
        <input
          type="radio"
          name="pricing_model"
          value="per_request"
          checked={value === "per_request"}
          onChange={(e) => onChange(e.target.value as "per_request")}
        />
        <div className="option-content">
          <strong>Per-Request Pricing</strong>
          <p>Customers pay based on their actual usage of your MCP server</p>
        </div>
      </label>
      
      <label className="pricing-model-option">
        <input
          type="radio"
          name="pricing_model"
          value="hybrid"
          checked={value === "hybrid"}
          onChange={(e) => onChange(e.target.value as "hybrid")}
        />
        <div className="option-content">
          <strong>Hybrid Model</strong>
          <p>Customers pay a base fee with included requests, plus per-request charges for overages</p>
        </div>
      </label>
    </div>
  </fieldset>
);

const PricingTierEditor = ({
  tiers,
  currencyType,
  onChange
}: {
  tiers: PricingTier[];
  currencyType: CurrencyCode;
  onChange: (tiers: PricingTier[]) => void;
}) => {
  const addTier = () => {
    const newTier: PricingTier = {
      max_requests: null,
      price_cents_per_request: 0,
      name: `Tier ${tiers.length + 1}`
    };
    onChange([...tiers, newTier]);
  };

  const updateTier = (index: number, updates: Partial<PricingTier>) => {
    const updatedTiers = tiers.map((tier, i) => 
      i === index ? { ...tier, ...updates } : tier
    );
    onChange(updatedTiers);
  };

  const removeTier = (index: number) => {
    onChange(tiers.filter((_, i) => i !== index));
  };

  return (
    <div className="pricing-tiers-editor">
      <div className="tiers-header">
        <h4>Pricing Tiers</h4>
        <button type="button" className="btn btn--small btn--secondary" onClick={addTier}>
          <Icon name="plus" />
          Add Tier
        </button>
      </div>
      
      {tiers.map((tier, index) => (
        <div key={index} className="pricing-tier">
          <div className="tier-header">
            <input
              type="text"
              value={tier.name}
              onChange={(e) => updateTier(index, { name: e.target.value })}
              placeholder="Tier name"
              className="tier-name-input"
            />
            <button
              type="button"
              className="btn btn--small btn--danger"
              onClick={() => removeTier(index)}
            >
              <Icon name="trash-2" />
            </button>
          </div>
          
          <div className="tier-config">
            <div className="form-group">
              <label>Max Requests</label>
              <input
                type="number"
                value={tier.max_requests || ""}
                onChange={(e) => updateTier(index, { 
                  max_requests: e.target.value ? parseInt(e.target.value) : null 
                })}
                placeholder="Unlimited"
                min="1"
              />
              <small>Leave empty for unlimited</small>
            </div>
            
            <div className="form-group">
              <label>Price per Request</label>
              <PriceInput
                currencyCode={currencyType}
                cents={tier.price_cents_per_request}
                onChange={(cents) => updateTier(index, { price_cents_per_request: cents || 0 })}
                placeholder="0.00"
              />
            </div>
          </div>
        </div>
      ))}
      
      {tiers.length === 0 && (
        <div className="empty-tiers">
          <p>No pricing tiers configured. Add a tier to enable tiered pricing.</p>
        </div>
      )}
    </div>
  );
};

const McpServerPricingEditor = ({
  pricingData,
  basePriceCents,
  currencyType,
  onChange
}: McpServerPricingEditorProps) => {
  const uid = React.useId();

  const updatePricingData = (updates: Partial<McpServerPricingData>) => {
    onChange({ ...pricingData, ...updates });
  };

  const estimateMonthlyRevenue = (requests: number) => {
    let cost = 0;
    
    if (pricingData.pricing_model === "fixed") {
      cost = basePriceCents;
    } else if (pricingData.pricing_model === "per_request") {
      const billableRequests = Math.max(0, requests - pricingData.free_tier_requests);
      cost = billableRequests * pricingData.per_request_price_cents;
    } else if (pricingData.pricing_model === "hybrid") {
      cost = basePriceCents;
      if (pricingData.monthly_request_limit && requests > pricingData.monthly_request_limit) {
        const overageRequests = requests - pricingData.monthly_request_limit;
        cost += overageRequests * pricingData.per_request_price_cents;
      }
    }
    
    return cost;
  };

  return (
    <div className="mcp-server-pricing-editor">
      <PricingModelSelector
        value={pricingData.pricing_model}
        onChange={(model) => updatePricingData({ pricing_model: model })}
      />

      {pricingData.pricing_model !== "fixed" && (
        <>
          <fieldset>
            <label htmlFor={`${uid}-free-tier`}>Free Tier Requests</label>
            <input
              id={`${uid}-free-tier`}
              type="number"
              value={pricingData.free_tier_requests}
              onChange={(e) => updatePricingData({ free_tier_requests: parseInt(e.target.value) || 0 })}
              min="0"
              placeholder="0"
            />
            <small>Number of free requests included for all customers</small>
          </fieldset>

          <fieldset>
            <label htmlFor={`${uid}-per-request-price`}>Price per Request</label>
            <PriceInput
              id={`${uid}-per-request-price`}
              currencyCode={currencyType}
              cents={pricingData.per_request_price_cents}
              onChange={(cents) => updatePricingData({ per_request_price_cents: cents || 0 })}
            />
            <small>Base price charged for each request beyond the free tier</small>
          </fieldset>

          <PricingTierEditor
            tiers={pricingData.pricing_tiers}
            currencyType={currencyType}
            onChange={(pricing_tiers) => updatePricingData({ pricing_tiers })}
          />
        </>
      )}

      {pricingData.pricing_model === "hybrid" && (
        <fieldset>
          <label htmlFor={`${uid}-monthly-limit`}>Monthly Request Limit</label>
          <input
            id={`${uid}-monthly-limit`}
            type="number"
            value={pricingData.monthly_request_limit || ""}
            onChange={(e) => updatePricingData({ 
              monthly_request_limit: e.target.value ? parseInt(e.target.value) : null 
            })}
            min="1"
            placeholder="Unlimited"
          />
          <small>Number of requests included in the base price</small>
        </fieldset>
      )}

      <div className="pricing-preview">
        <h4>Pricing Preview</h4>
        <div className="preview-scenarios">
          {[100, 1000, 10000].map((requests) => (
            <div key={requests} className="scenario">
              <span className="scenario-label">{requests.toLocaleString()} requests/month:</span>
              <span className="scenario-price">
                {formatPriceCentsWithCurrencySymbol(currencyType, estimateMonthlyRevenue(requests))}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default McpServerPricingEditor;
