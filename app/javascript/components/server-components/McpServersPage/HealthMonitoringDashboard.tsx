import * as React from "react";
import { Icon } from "$app/components/Icons";

type HealthDashboardData = {
  total_servers: number;
  healthy_servers: number;
  unhealthy_servers: number;
  unknown_status_servers: number;
  recent_checks: {
    total_checks: number;
    successful_checks: number;
    failed_checks: number;
    average_response_time: number;
  };
  server_health_overview: Array<{
    id: number;
    endpoint_url: string;
    health_status: "healthy" | "unhealthy" | "unknown";
    last_check_at: string | null;
    uptime_percentage: number;
    average_response_time: number;
    consecutive_failures: number;
  }>;
  uptime_trends: Array<{
    date: string;
    uptime_percentage: number;
    total_checks: number;
    successful_checks: number;
  }>;
};

type HealthMonitoringDashboardProps = {
  data: HealthDashboardData;
  onRefresh: () => void;
  onTriggerHealthCheck: (serverId: number) => void;
};

const HealthStatusBadge = ({ status }: { status: "healthy" | "unhealthy" | "unknown" }) => {
  const statusConfig = {
    healthy: { color: "green", icon: "check-circle", text: "Healthy" },
    unhealthy: { color: "red", icon: "x-circle", text: "Unhealthy" },
    unknown: { color: "gray", icon: "help-circle", text: "Unknown" }
  };

  const config = statusConfig[status];

  return (
    <span className={`health-badge health-badge--${config.color}`}>
      <Icon name={config.icon} />
      {config.text}
    </span>
  );
};

const HealthMetricCard = ({ title, value, subtitle, icon, color = "blue" }: {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  color?: string;
}) => (
  <div className={`health-metric-card health-metric-card--${color}`}>
    <div className="health-metric-card__icon">
      <Icon name={icon} />
    </div>
    <div className="health-metric-card__content">
      <h3 className="health-metric-card__title">{title}</h3>
      <div className="health-metric-card__value">{value}</div>
      {subtitle && <div className="health-metric-card__subtitle">{subtitle}</div>}
    </div>
  </div>
);

const ServerHealthRow = ({ 
  server, 
  onTriggerHealthCheck 
}: { 
  server: HealthDashboardData['server_health_overview'][0];
  onTriggerHealthCheck: (serverId: number) => void;
}) => (
  <tr className="server-health-row">
    <td>
      <div className="server-info">
        <div className="server-url">{server.endpoint_url}</div>
        <div className="server-meta">
          Last checked: {server.last_check_at ? new Date(server.last_check_at).toLocaleString() : 'Never'}
        </div>
      </div>
    </td>
    <td>
      <HealthStatusBadge status={server.health_status} />
    </td>
    <td>
      <div className="uptime-cell">
        <span className="uptime-percentage">{server.uptime_percentage.toFixed(1)}%</span>
        <div className="uptime-bar">
          <div 
            className="uptime-fill" 
            style={{ width: `${server.uptime_percentage}%` }}
          />
        </div>
      </div>
    </td>
    <td>
      <span className="response-time">{server.average_response_time.toFixed(0)}ms</span>
    </td>
    <td>
      {server.consecutive_failures > 0 && (
        <span className="failure-count">{server.consecutive_failures} failures</span>
      )}
    </td>
    <td>
      <button
        className="btn btn--small btn--secondary"
        onClick={() => onTriggerHealthCheck(server.id)}
      >
        <Icon name="refresh-cw" />
        Check Now
      </button>
    </td>
  </tr>
);

const HealthMonitoringDashboard = ({ 
  data, 
  onRefresh, 
  onTriggerHealthCheck 
}: HealthMonitoringDashboardProps) => {
  const healthPercentage = data.total_servers > 0 
    ? (data.healthy_servers / data.total_servers * 100).toFixed(1)
    : '0';

  const recentSuccessRate = data.recent_checks.total_checks > 0
    ? (data.recent_checks.successful_checks / data.recent_checks.total_checks * 100).toFixed(1)
    : '0';

  return (
    <div className="health-monitoring-dashboard">
      <div className="dashboard-header">
        <h2>MCP Server Health Monitoring</h2>
        <button className="btn btn--primary" onClick={onRefresh}>
          <Icon name="refresh-cw" />
          Refresh
        </button>
      </div>

      <div className="health-metrics-grid">
        <HealthMetricCard
          title="Total Servers"
          value={data.total_servers}
          icon="server"
          color="blue"
        />
        <HealthMetricCard
          title="Healthy Servers"
          value={data.healthy_servers}
          subtitle={`${healthPercentage}% healthy`}
          icon="check-circle"
          color="green"
        />
        <HealthMetricCard
          title="Unhealthy Servers"
          value={data.unhealthy_servers}
          icon="x-circle"
          color="red"
        />
        <HealthMetricCard
          title="Recent Success Rate"
          value={`${recentSuccessRate}%`}
          subtitle={`${data.recent_checks.total_checks} checks`}
          icon="activity"
          color="purple"
        />
        <HealthMetricCard
          title="Avg Response Time"
          value={`${data.recent_checks.average_response_time.toFixed(0)}ms`}
          icon="clock"
          color="orange"
        />
      </div>

      <div className="uptime-trends-section">
        <h3>7-Day Uptime Trend</h3>
        <div className="uptime-chart">
          {data.uptime_trends.map((trend, index) => (
            <div key={trend.date} className="uptime-bar-container">
              <div className="uptime-date">{new Date(trend.date).toLocaleDateString()}</div>
              <div className="uptime-bar-wrapper">
                <div 
                  className="uptime-bar-fill"
                  style={{ 
                    height: `${trend.uptime_percentage}%`,
                    backgroundColor: trend.uptime_percentage >= 95 ? '#10b981' : 
                                   trend.uptime_percentage >= 80 ? '#f59e0b' : '#ef4444'
                  }}
                />
              </div>
              <div className="uptime-percentage-label">{trend.uptime_percentage.toFixed(0)}%</div>
            </div>
          ))}
        </div>
      </div>

      <div className="servers-health-table-section">
        <h3>Server Health Overview</h3>
        <div className="table-container">
          <table className="servers-health-table">
            <thead>
              <tr>
                <th>Server</th>
                <th>Status</th>
                <th>Uptime (24h)</th>
                <th>Avg Response</th>
                <th>Issues</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {data.server_health_overview.map((server) => (
                <ServerHealthRow
                  key={server.id}
                  server={server}
                  onTriggerHealthCheck={onTriggerHealthCheck}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default HealthMonitoringDashboard;
