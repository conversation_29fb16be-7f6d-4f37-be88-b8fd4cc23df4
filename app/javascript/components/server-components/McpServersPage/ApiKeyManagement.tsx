import * as React from "react";
import { Icon } from "$app/components/Icons";

type ApiKey = {
  id: number;
  name: string;
  masked_key: string;
  status: "active" | "suspended" | "revoked";
  permissions: string[];
  rate_limits: {
    requests_per_hour: number;
    requests_per_day: number;
    requests_per_month: number;
  };
  expires_at: string | null;
  last_used_at: string | null;
  created_at: string;
  usage_stats: {
    total_requests: number;
    successful_requests: number;
    failed_requests: number;
    success_rate: number;
    average_response_time: number;
  };
};

type ApiKeyManagementProps = {
  apiKeys: ApiKey[];
  onCreateApiKey: (data: { purchase_id: number; link_id: number; name: string }) => void;
  onRevokeApiKey: (apiKeyId: number) => void;
  onSuspendApiKey: (apiKeyId: number) => void;
  onActivateApiKey: (apiKeyId: number) => void;
  onRegenerateApiKey: (apiKeyId: number) => void;
  onRefresh: () => void;
};

const StatusBadge = ({ status }: { status: "active" | "suspended" | "revoked" }) => {
  const statusConfig = {
    active: { color: "green", text: "Active" },
    suspended: { color: "yellow", text: "Suspended" },
    revoked: { color: "red", text: "Revoked" }
  };

  const config = statusConfig[status];

  return (
    <span className={`status-badge status-badge--${config.color}`}>
      {config.text}
    </span>
  );
};

const ApiKeyCard = ({ 
  apiKey, 
  onRevoke, 
  onSuspend, 
  onActivate, 
  onRegenerate 
}: {
  apiKey: ApiKey;
  onRevoke: () => void;
  onSuspend: () => void;
  onActivate: () => void;
  onRegenerate: () => void;
}) => {
  const [showActions, setShowActions] = React.useState(false);
  const [showKey, setShowKey] = React.useState(false);

  const handleCopyKey = () => {
    navigator.clipboard.writeText(apiKey.masked_key);
    // You might want to show a toast notification here
  };

  return (
    <div className="api-key-card">
      <div className="api-key-card__header">
        <div className="api-key-info">
          <h3 className="api-key-name">{apiKey.name}</h3>
          <StatusBadge status={apiKey.status} />
        </div>
        <button
          className="actions-toggle"
          onClick={() => setShowActions(!showActions)}
        >
          <Icon name="more-horizontal" />
        </button>
      </div>

      <div className="api-key-card__content">
        <div className="api-key-display">
          <label>API Key:</label>
          <div className="key-container">
            <code className="api-key-value">
              {showKey ? apiKey.masked_key : "••••••••••••••••••••••••••••••••"}
            </code>
            <button
              className="btn btn--small btn--secondary"
              onClick={() => setShowKey(!showKey)}
            >
              <Icon name={showKey ? "eye-off" : "eye"} />
            </button>
            <button
              className="btn btn--small btn--secondary"
              onClick={handleCopyKey}
            >
              <Icon name="copy" />
            </button>
          </div>
        </div>

        <div className="api-key-metadata">
          <div className="metadata-row">
            <span className="label">Permissions:</span>
            <span className="value">{apiKey.permissions.join(", ") || "None"}</span>
          </div>
          <div className="metadata-row">
            <span className="label">Rate Limit:</span>
            <span className="value">
              {apiKey.rate_limits.requests_per_hour.toLocaleString()}/hour
            </span>
          </div>
          <div className="metadata-row">
            <span className="label">Last Used:</span>
            <span className="value">
              {apiKey.last_used_at 
                ? new Date(apiKey.last_used_at).toLocaleDateString()
                : "Never"
              }
            </span>
          </div>
          <div className="metadata-row">
            <span className="label">Expires:</span>
            <span className="value">
              {apiKey.expires_at 
                ? new Date(apiKey.expires_at).toLocaleDateString()
                : "Never"
              }
            </span>
          </div>
        </div>

        <div className="usage-stats">
          <h4>Usage Statistics</h4>
          <div className="stats-grid">
            <div className="stat">
              <span className="stat-label">Total Requests</span>
              <span className="stat-value">{apiKey.usage_stats.total_requests.toLocaleString()}</span>
            </div>
            <div className="stat">
              <span className="stat-label">Success Rate</span>
              <span className="stat-value">{apiKey.usage_stats.success_rate.toFixed(1)}%</span>
            </div>
            <div className="stat">
              <span className="stat-label">Avg Response</span>
              <span className="stat-value">{apiKey.usage_stats.average_response_time.toFixed(0)}ms</span>
            </div>
          </div>
        </div>
      </div>

      {showActions && (
        <div className="api-key-actions">
          {apiKey.status === "active" && (
            <>
              <button className="btn btn--small btn--warning" onClick={onSuspend}>
                <Icon name="pause" />
                Suspend
              </button>
              <button className="btn btn--small btn--secondary" onClick={onRegenerate}>
                <Icon name="refresh-cw" />
                Regenerate
              </button>
            </>
          )}
          {apiKey.status === "suspended" && (
            <button className="btn btn--small btn--success" onClick={onActivate}>
              <Icon name="play" />
              Activate
            </button>
          )}
          {apiKey.status !== "revoked" && (
            <button className="btn btn--small btn--danger" onClick={onRevoke}>
              <Icon name="trash-2" />
              Revoke
            </button>
          )}
        </div>
      )}
    </div>
  );
};

const ApiKeyManagement = ({
  apiKeys,
  onCreateApiKey,
  onRevokeApiKey,
  onSuspendApiKey,
  onActivateApiKey,
  onRegenerateApiKey,
  onRefresh
}: ApiKeyManagementProps) => {
  const [showCreateForm, setShowCreateForm] = React.useState(false);
  const [newKeyName, setNewKeyName] = React.useState("");

  const handleCreateKey = (e: React.FormEvent) => {
    e.preventDefault();
    if (newKeyName.trim()) {
      // This would need purchase_id and link_id from props or context
      // onCreateApiKey({ purchase_id: ?, link_id: ?, name: newKeyName.trim() });
      setNewKeyName("");
      setShowCreateForm(false);
    }
  };

  const activeKeys = apiKeys.filter(key => key.status === "active");
  const inactiveKeys = apiKeys.filter(key => key.status !== "active");

  return (
    <div className="api-key-management">
      <div className="management-header">
        <h2>API Key Management</h2>
        <div className="header-actions">
          <button className="btn btn--secondary" onClick={onRefresh}>
            <Icon name="refresh-cw" />
            Refresh
          </button>
          <button 
            className="btn btn--primary" 
            onClick={() => setShowCreateForm(true)}
          >
            <Icon name="plus" />
            Create API Key
          </button>
        </div>
      </div>

      {showCreateForm && (
        <div className="create-key-form">
          <form onSubmit={handleCreateKey}>
            <div className="form-group">
              <label htmlFor="keyName">API Key Name</label>
              <input
                id="keyName"
                type="text"
                value={newKeyName}
                onChange={(e) => setNewKeyName(e.target.value)}
                placeholder="Enter a name for your API key"
                required
              />
            </div>
            <div className="form-actions">
              <button type="button" className="btn btn--secondary" onClick={() => setShowCreateForm(false)}>
                Cancel
              </button>
              <button type="submit" className="btn btn--primary">
                Create Key
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="api-keys-summary">
        <div className="summary-stat">
          <span className="stat-label">Total Keys</span>
          <span className="stat-value">{apiKeys.length}</span>
        </div>
        <div className="summary-stat">
          <span className="stat-label">Active Keys</span>
          <span className="stat-value">{activeKeys.length}</span>
        </div>
        <div className="summary-stat">
          <span className="stat-label">Inactive Keys</span>
          <span className="stat-value">{inactiveKeys.length}</span>
        </div>
      </div>

      <div className="api-keys-sections">
        {activeKeys.length > 0 && (
          <div className="api-keys-section">
            <h3>Active API Keys</h3>
            <div className="api-keys-grid">
              {activeKeys.map((apiKey) => (
                <ApiKeyCard
                  key={apiKey.id}
                  apiKey={apiKey}
                  onRevoke={() => onRevokeApiKey(apiKey.id)}
                  onSuspend={() => onSuspendApiKey(apiKey.id)}
                  onActivate={() => onActivateApiKey(apiKey.id)}
                  onRegenerate={() => onRegenerateApiKey(apiKey.id)}
                />
              ))}
            </div>
          </div>
        )}

        {inactiveKeys.length > 0 && (
          <div className="api-keys-section">
            <h3>Inactive API Keys</h3>
            <div className="api-keys-grid">
              {inactiveKeys.map((apiKey) => (
                <ApiKeyCard
                  key={apiKey.id}
                  apiKey={apiKey}
                  onRevoke={() => onRevokeApiKey(apiKey.id)}
                  onSuspend={() => onSuspendApiKey(apiKey.id)}
                  onActivate={() => onActivateApiKey(apiKey.id)}
                  onRegenerate={() => onRegenerateApiKey(apiKey.id)}
                />
              ))}
            </div>
          </div>
        )}

        {apiKeys.length === 0 && (
          <div className="empty-state">
            <Icon name="key" />
            <h3>No API Keys</h3>
            <p>You don't have any API keys yet. Create one to start using MCP servers.</p>
            <button 
              className="btn btn--primary" 
              onClick={() => setShowCreateForm(true)}
            >
              Create Your First API Key
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApiKeyManagement;
