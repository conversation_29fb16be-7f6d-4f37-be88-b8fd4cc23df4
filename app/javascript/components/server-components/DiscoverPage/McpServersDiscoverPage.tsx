import * as React from "react";
import { useLoaderData, useSearchParams } from "react-router-dom";
import McpServerCard, { McpServerCardData } from "./McpServerCard";
import { Icon } from "$app/components/Icons";

type McpServersDiscoverPageProps = {
  search_results: {
    total: number;
    products: McpServerCardData[];
    tags_data: Array<{ key: string; doc_count: number }>;
    filetypes_data: Array<{ key: string; doc_count: number }>;
  };
  currency_code: string;
  search_offset: number;
  mcp_servers_only: boolean;
};

const McpServersDiscoverPage = () => {
  const data = useLoaderData() as McpServersDiscoverPageProps;
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = React.useState(searchParams.get("query") || "");

  const { search_results } = data;
  const mcpServers = search_results.products;

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const newParams = new URLSearchParams(searchParams);
    if (searchQuery.trim()) {
      newParams.set("query", searchQuery.trim());
    } else {
      newParams.delete("query");
    }
    setSearchParams(newParams);
  };

  const handleFilterByTag = (tag: string) => {
    const newParams = new URLSearchParams(searchParams);
    const currentTags = newParams.get("tags")?.split(",") || [];
    
    if (currentTags.includes(tag)) {
      const updatedTags = currentTags.filter(t => t !== tag);
      if (updatedTags.length > 0) {
        newParams.set("tags", updatedTags.join(","));
      } else {
        newParams.delete("tags");
      }
    } else {
      newParams.set("tags", [...currentTags, tag].join(","));
    }
    
    setSearchParams(newParams);
  };

  const handleSortChange = (sortBy: string) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("sort", sortBy);
    setSearchParams(newParams);
  };

  const activeTags = searchParams.get("tags")?.split(",") || [];
  const currentSort = searchParams.get("sort") || "featured";

  return (
    <div className="mcp-servers-discover-page">
      <div className="page-header">
        <div className="page-header__content">
          <h1 className="page-title">
            <Icon name="server" />
            MCP Server Marketplace
          </h1>
          <p className="page-subtitle">
            Discover and purchase Model Context Protocol servers to extend your AI applications
          </p>
        </div>
      </div>

      <div className="discover-content">
        <div className="discover-sidebar">
          <div className="search-section">
            <form onSubmit={handleSearch} className="search-form">
              <div className="search-input-group">
                <input
                  type="text"
                  placeholder="Search MCP servers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="search-input"
                />
                <button type="submit" className="search-button">
                  <Icon name="search" />
                </button>
              </div>
            </form>
          </div>

          <div className="filter-section">
            <h3 className="filter-title">Sort by</h3>
            <div className="sort-options">
              {[
                { value: "featured", label: "Featured" },
                { value: "newest", label: "Newest" },
                { value: "price_asc", label: "Price: Low to High" },
                { value: "price_desc", label: "Price: High to Low" },
                { value: "most_reviewed", label: "Most Popular" }
              ].map((option) => (
                <label key={option.value} className="sort-option">
                  <input
                    type="radio"
                    name="sort"
                    value={option.value}
                    checked={currentSort === option.value}
                    onChange={() => handleSortChange(option.value)}
                  />
                  {option.label}
                </label>
              ))}
            </div>
          </div>

          {search_results.tags_data.length > 0 && (
            <div className="filter-section">
              <h3 className="filter-title">Popular Tags</h3>
              <div className="tag-filters">
                {search_results.tags_data.slice(0, 10).map((tag) => (
                  <button
                    key={tag.key}
                    className={`tag-filter ${activeTags.includes(tag.key) ? "tag-filter--active" : ""}`}
                    onClick={() => handleFilterByTag(tag.key)}
                  >
                    {tag.key}
                    <span className="tag-count">({tag.doc_count})</span>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="discover-main">
          <div className="results-header">
            <div className="results-info">
              <span className="results-count">
                {search_results.total.toLocaleString()} MCP server{search_results.total !== 1 ? "s" : ""} found
              </span>
              {searchParams.get("query") && (
                <span className="search-query">
                  for "{searchParams.get("query")}"
                </span>
              )}
            </div>

            {activeTags.length > 0 && (
              <div className="active-filters">
                <span className="filter-label">Filtered by:</span>
                {activeTags.map((tag) => (
                  <span key={tag} className="active-filter">
                    {tag}
                    <button
                      className="remove-filter"
                      onClick={() => handleFilterByTag(tag)}
                    >
                      <Icon name="x" />
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          {mcpServers.length > 0 ? (
            <div className="mcp-servers-grid">
              {mcpServers.map((mcpServer) => (
                <McpServerCard
                  key={mcpServer.id}
                  mcpServer={mcpServer}
                  showSeller={true}
                />
              ))}
            </div>
          ) : (
            <div className="empty-state">
              <Icon name="search" />
              <h3>No MCP servers found</h3>
              <p>
                {searchParams.get("query") 
                  ? `No servers match your search for "${searchParams.get("query")}"`
                  : "No MCP servers are currently available"
                }
              </p>
              {(searchParams.get("query") || activeTags.length > 0) && (
                <button
                  className="btn btn--secondary"
                  onClick={() => {
                    setSearchParams({});
                    setSearchQuery("");
                  }}
                >
                  Clear filters
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default McpServersDiscoverPage;
