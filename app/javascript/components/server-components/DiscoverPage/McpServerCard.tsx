import * as React from "react";
import { formatPriceCentsWithCurrencySymbol, CurrencyCode } from "$app/utils/currency";
import { formatDate } from "$app/utils/date";
import { Icon } from "$app/components/Icons";

export type McpServerCardData = {
  id: string;
  name: string;
  description: string;
  endpoint_url: string;
  price_cents: number;
  currency_code: string;
  health_status: "healthy" | "unhealthy" | "unknown";
  last_health_check_at: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  supported_tools: string[];
  github_url?: string;
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  average_response_time_ms: number;
  success_rate: number;
  published: boolean;
  seller: {
    id: string;
    name: string;
    username: string;
    profile_url: string;
  };
  url: string;
  purchase_url: string;
  tags: string[];
  server_metadata: Record<string, any>;
  recommended_by?: string;
  target?: string;
  query?: string;
};

type McpServerCardProps = {
  mcpServer: McpServerCardData;
  showSeller?: boolean;
};

const HealthBadge = ({ status }: { status: "healthy" | "unhealthy" | "unknown" }) => {
  const statusConfig = {
    healthy: { color: "green", icon: "check-circle", text: "Healthy" },
    unhealthy: { color: "red", icon: "x-circle", text: "Unhealthy" },
    unknown: { color: "gray", icon: "help-circle", text: "Unknown" }
  };

  const config = statusConfig[status];

  return (
    <span className={`health-badge health-badge--${config.color}`}>
      <Icon name={config.icon} />
      {config.text}
    </span>
  );
};

const McpServerCard = ({ mcpServer, showSeller = true }: McpServerCardProps) => {
  const handleCardClick = () => {
    window.open(mcpServer.url, '_blank');
  };

  return (
    <div className="mcp-server-card" onClick={handleCardClick}>
      <div className="mcp-server-card__header">
        <h3 className="mcp-server-card__title">{mcpServer.name}</h3>
        <div className="mcp-server-card__price">
          {formatPriceCentsWithCurrencySymbol(
            mcpServer.currency_code as CurrencyCode,
            mcpServer.price_cents,
            { symbolFormat: "short" }
          )}
        </div>
      </div>

      <div className="mcp-server-card__description">
        {mcpServer.description}
      </div>

      <div className="mcp-server-card__metadata">
        <div className="mcp-server-card__endpoint">
          <Icon name="globe" />
          <span className="endpoint-url">{mcpServer.endpoint_url}</span>
        </div>

        {mcpServer.supported_tools.length > 0 && (
          <div className="mcp-server-card__tools">
            <Icon name="tool" />
            <span>
              {mcpServer.supported_tools.length} tool{mcpServer.supported_tools.length !== 1 ? "s" : ""}
            </span>
          </div>
        )}

        {mcpServer.github_url && (
          <div className="mcp-server-card__github">
            <Icon name="github" />
            <a 
              href={mcpServer.github_url} 
              target="_blank" 
              rel="noopener noreferrer"
              onClick={(e) => e.stopPropagation()}
            >
              GitHub
            </a>
          </div>
        )}
      </div>

      <div className="mcp-server-card__stats">
        <div className="stat">
          <span className="stat__label">Health</span>
          <HealthBadge status={mcpServer.health_status} />
        </div>

        {mcpServer.total_requests > 0 && (
          <>
            <div className="stat">
              <span className="stat__label">Success Rate</span>
              <span className="stat__value">{mcpServer.success_rate}%</span>
            </div>

            <div className="stat">
              <span className="stat__label">Avg Response</span>
              <span className="stat__value">{mcpServer.average_response_time_ms}ms</span>
            </div>

            <div className="stat">
              <span className="stat__label">Total Requests</span>
              <span className="stat__value">{mcpServer.total_requests.toLocaleString()}</span>
            </div>
          </>
        )}
      </div>

      {showSeller && (
        <div className="mcp-server-card__seller">
          <span className="seller-label">by</span>
          <a 
            href={mcpServer.seller.profile_url} 
            className="seller-link"
            onClick={(e) => e.stopPropagation()}
          >
            {mcpServer.seller.name}
          </a>
        </div>
      )}

      {mcpServer.tags.length > 0 && (
        <div className="mcp-server-card__tags">
          {mcpServer.tags.slice(0, 3).map((tag) => (
            <span key={tag} className="tag">
              {tag}
            </span>
          ))}
          {mcpServer.tags.length > 3 && (
            <span className="tag tag--more">
              +{mcpServer.tags.length - 3} more
            </span>
          )}
        </div>
      )}

      <div className="mcp-server-card__footer">
        <span className="created-date">
          Created {formatDate(mcpServer.created_at)}
        </span>
        <button 
          className="btn btn--primary btn--small"
          onClick={(e) => {
            e.stopPropagation();
            window.open(mcpServer.purchase_url, '_blank');
          }}
        >
          {mcpServer.price_cents > 0 ? 'Purchase' : 'Get Free'}
        </button>
      </div>
    </div>
  );
};

export default McpServerCard;
