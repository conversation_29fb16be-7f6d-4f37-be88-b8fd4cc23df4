# frozen_string_literal: true

class McpServerConfig < ApplicationRecord
  include JsonData
  include TimestampScopes

  belongs_to :link, class_name: "Link"
  has_many :mcp_server_usages, foreign_key: "link_id", primary_key: "link_id", dependent: :destroy
  has_many :mcp_server_health_checks, dependent: :destroy

  validates :endpoint_url, presence: true, format: { with: URI::DEFAULT_PARSER.make_regexp(%w[http https]) }
  validates :link_id, presence: true, uniqueness: true
  validates :health_status, inclusion: { in: %w[healthy unhealthy unknown] }

  attr_json_data_accessor :supported_tools, default: -> { [] }
  attr_json_data_accessor :server_metadata, default: -> { {} }

  scope :active, -> { where(is_active: true) }
  scope :inactive, -> { where(is_active: false) }
  scope :healthy, -> { where(health_status: "healthy") }
  scope :unhealthy, -> { where(health_status: "unhealthy") }
  scope :unknown_health, -> { where(health_status: "unknown") }

  before_validation :set_default_health_status, on: :create

  def healthy?
    health_status == "healthy"
  end

  def unhealthy?
    health_status == "unhealthy"
  end

  def unknown_health?
    health_status == "unknown"
  end

  def update_health_status!(status)
    update!(health_status: status, last_health_check_at: Time.current)
  end

  def needs_health_check?
    last_health_check_at.nil? || last_health_check_at < 5.minutes.ago
  end

  def schedule_health_check!
    McpServerHealthCheckJob.perform_async(id)
  end

  def uptime_percentage(days: 7)
    mcp_server_health_checks.uptime_percentage(start_date: days.days.ago)
  end

  def average_response_time(days: 7)
    mcp_server_health_checks.average_response_time(start_date: days.days.ago)
  end

  def recent_health_summary
    mcp_server_health_checks.health_summary
  end

  def last_successful_check
    mcp_server_health_checks.successful.order(checked_at: :desc).first
  end

  def last_failed_check
    mcp_server_health_checks.failed.order(checked_at: :desc).first
  end

  def consecutive_failures
    return 0 if healthy?

    # Count consecutive failures from the most recent check
    recent_checks = mcp_server_health_checks.order(checked_at: :desc).limit(10)
    consecutive_count = 0

    recent_checks.each do |check|
      if check.failure?
        consecutive_count += 1
      else
        break
      end
    end

    consecutive_count
  end

  def should_alert_for_downtime?
    consecutive_failures >= 3 # Alert after 3 consecutive failures
  end

  def as_json(options = {})
    super(options).merge(
      supported_tools: supported_tools,
      server_metadata: server_metadata
    )
  end

  private

  def set_default_health_status
    self.health_status ||= "unknown"
  end
end
