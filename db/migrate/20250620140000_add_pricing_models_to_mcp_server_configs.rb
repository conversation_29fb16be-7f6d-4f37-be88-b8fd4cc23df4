# frozen_string_literal: true

class AddPricingModelsToMcpServerConfigs < ActiveRecord::Migration[7.1]
  def change
    add_column :mcp_server_configs, :pricing_model, :string, default: "fixed", null: false
    add_column :mcp_server_configs, :per_request_price_cents, :integer, default: 0, null: false
    add_column :mcp_server_configs, :monthly_request_limit, :integer
    add_column :mcp_server_configs, :free_tier_requests, :integer, default: 0, null: false
    add_column :mcp_server_configs, :pricing_tiers, :json, default: []

    add_index :mcp_server_configs, :pricing_model
  end
end
